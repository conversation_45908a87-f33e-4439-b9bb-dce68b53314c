{"name": "flowdesk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-query": "^5.84.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "lucide-react": "^0.534.0", "next": "15.4.4", "react": "19.1.0", "react-big-calendar": "^1.19.4", "react-dom": "19.1.0", "react-hot-toast": "^2.5.2", "supabase": "^2.33.9"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19.1.9", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}