import React, { useState, useEffect } from "react";
import { QuickLink } from "@/entities/QuickLink";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Plus, ExternalLink, Edit, Trash2, Link as LinkIcon,
  Globe, Mail, Github, Twitter, Linkedin, Youtube, Instagram,
  Calendar, Clock, BookOpen, FileText, Camera, Music, Settings, Palette
} from "lucide-react";

const iconComponents = {
  Globe, Mail, Github, Twitter, Linkedin, Youtube, Instagram,
  Calendar, Clock, BookOpen, FileText, Camera, Music, Settings, Palette,
  LinkIcon
};

const iconOptions = [
  'Globe', 'Mail', 'Github', 'Twitter', 'Linkedin', 'Youtube', 'Instagram',
  'Calendar', 'Clock', 'BookOpen', 'FileText', 'Camera', 'Music', 'Settings', 'Palette'
];

const colorOptions = [
  { name: 'Blue', value: 'blue' },
  { name: 'Purple', value: 'purple' },
  { name: 'Green', value: 'green' },
  { name: 'Red', value: 'red' },
  { name: 'Yellow', value: 'yellow' },
  { name: 'Pink', value: 'pink' }
];

export default function QuickLinksWidget() {
  const [links, setLinks] = useState([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingLink, setEditingLink] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    url: '',
    icon: 'LinkIcon',
    color: 'blue'
  });

  useEffect(() => {
    loadLinks();
  }, []);

  const loadLinks = async () => {
    const data = await QuickLink.list('-created_date');
    setLinks(data);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (editingLink) {
      await QuickLink.update(editingLink.id, formData);
    } else {
      await QuickLink.create(formData);
    }
    
    setIsDialogOpen(false);
    setEditingLink(null);
    setFormData({ title: '', url: '', icon: 'LinkIcon', color: 'blue' });
    loadLinks();
  };

  const handleEdit = (link) => {
    setEditingLink(link);
    setFormData({
      title: link.title,
      url: link.url,
      icon: link.icon || 'LinkIcon',
      color: link.color || 'blue'
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id) => {
    await QuickLink.delete(id);
    loadLinks();
  };

  const getColorClass = (color) => {
    const colorMap = {
      blue: 'from-blue-500 to-blue-600',
      purple: 'from-purple-500 to-purple-600',
      green: 'from-green-500 to-green-600',
      red: 'from-red-500 to-red-600',
      yellow: 'from-yellow-500 to-yellow-600',
      pink: 'from-pink-500 to-pink-600'
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <Card className="glass-effect border-slate-200/20 dark:border-slate-700/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Quick Links</CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-1" />
                Add
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{editingLink ? 'Edit Link' : 'Add New Link'}</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="url">URL</Label>
                  <Input
                    id="url"
                    type="url"
                    value={formData.url}
                    onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label>Icon</Label>
                  <Select 
                    value={formData.icon} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, icon: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {iconOptions.map(icon => (
                        <SelectItem key={icon} value={icon}>
                          <div className="flex items-center gap-2">
                            {React.createElement(iconComponents[icon] || LinkIcon, { className: "w-4 h-4" })}
                            {icon}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Color</Label>
                  <Select 
                    value={formData.color} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, color: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map(color => (
                        <SelectItem key={color.value} value={color.value}>
                          {color.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex gap-2">
                  <Button type="submit" className="flex-1">
                    {editingLink ? 'Update' : 'Add'} Link
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {links.map((link) => {
            const IconComponent = iconComponents[link.icon] || LinkIcon;
            return (
              <div key={link.id} className="group relative">
                <a
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`flex flex-col items-center justify-center p-4 rounded-xl bg-gradient-to-br ${getColorClass(link.color)} text-white transition-all duration-200 hover:scale-105 hover:shadow-lg`}
                >
                  <IconComponent className="w-6 h-6 mb-2" />
                  <span className="text-sm font-medium text-center">{link.title}</span>
                  <ExternalLink className="w-3 h-3 absolute top-2 right-2 opacity-70" />
                </a>
                <div className="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                  <Button
                    size="icon"
                    variant="secondary"
                    className="w-6 h-6 bg-white/20 hover:bg-white/30"
                    onClick={() => handleEdit(link)}
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    size="icon"
                    variant="secondary"
                    className="w-6 h-6 bg-white/20 hover:bg-red-500/80"
                    onClick={() => handleDelete(link.id)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}